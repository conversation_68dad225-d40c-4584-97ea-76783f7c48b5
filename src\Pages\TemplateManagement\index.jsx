import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Box, 
  Typo<PERSON>, 
  <PERSON><PERSON>ield, 
  <PERSON>ton, 
  Card, 
  CardContent, 
  CardActions, 
  Grid, 
  Pagination, 
  IconButton,
  Chip,
  InputAdornment,
  Divider
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  ToggleOn as ToggleOnIcon,
  ToggleOff as ToggleOffIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import { getBadgeTemplates, deleteBadgeTemplate, updateBadgeTemplate } from '../../api/badge';
import CreateTemplateModal from '../../Components/TemplateManagement/CreateTemplateModal';
import { getUnsavedTemplates, removeTemplateFromStorage } from '../../utils/templateStorage';

const TemplateManagement = () => {
  const navigate = useNavigate();
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalTemplates, setTotalTemplates] = useState(0);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const templatesPerPage = 12;
  const [unsavedTemplates, setUnsavedTemplates] = useState([]);

  // Load unsaved templates
  useEffect(() => {
    const loadUnsavedTemplates = () => {
      const unsaved = getUnsavedTemplates();
      setUnsavedTemplates(unsaved);
    };
    
    loadUnsavedTemplates();
    
    // Refresh on focus and interval
    const handleFocus = () => loadUnsavedTemplates();
    const interval = setInterval(loadUnsavedTemplates, 5000); // Check every 5 seconds
    
    window.addEventListener('focus', handleFocus);
    
    return () => {
      window.removeEventListener('focus', handleFocus);
      clearInterval(interval);
    };
  }, []);

  // Fetch templates
  const fetchTemplates = async (page = 1, search = '') => {
    setLoading(true);
    try {
      const params = {
        page,
        limit: templatesPerPage,
        search: search.trim() || undefined,
        sortBy: 'created_at',
        sortOrder: 'desc'
      };
      
      const response = await getBadgeTemplates(params);
      console.log('API Response:', response); // Debug log

      // Handle different possible response structures
      const templatesData = response.data?.data?.data || response.data?.data || response.data || response || [];
      const total = response.data?.data?.totalItems || response.data?.totalItems || response.data?.total || response.total || 0;

      setTemplates(Array.isArray(templatesData) ? templatesData : []);
      setTotalPages(Math.ceil(total / templatesPerPage));
      setTotalTemplates(total);
    } catch (error) {
      console.error('Error fetching templates:', error);
      console.error('Error response:', error.response?.data);

      // For development, show a more helpful error message
      if (process.env.NODE_ENV === 'development') {
        console.warn('Using empty array as fallback for templates');
        toast.error('API Error - Check console for details. Using empty template list.');
      } else {
        toast.error('Failed to fetch templates: ' + (error.response?.data?.message || error.message));
      }

      setTemplates([]);
      setTotalPages(1);
      setTotalTemplates(0);
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    fetchTemplates(currentPage, searchTerm);
  }, [currentPage]);

  // Search handler with debounce
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (currentPage === 1) {
        fetchTemplates(1, searchTerm);
      } else {
        setCurrentPage(1);
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  // Handle page change
  const handlePageChange = (event, page) => {
    setCurrentPage(page);
  };

  // Handle template deletion
  const handleDeleteTemplate = async (badgeTemplateId, templateName) => {
    if (window.confirm(`Are you sure you want to delete "${templateName}"? This action cannot be undone.`)) {
      try {
        await deleteBadgeTemplate(badgeTemplateId);
        toast.success('Template deleted successfully');
        fetchTemplates(currentPage, searchTerm);
      } catch (error) {
        console.error('Error deleting template:', error);
        toast.error('Failed to delete template');
      }
    }
  };

  // Handle template edit/open
  const handleOpenTemplate = (badgeTemplateId) => {
    navigate(`/id-designer/${badgeTemplateId}`);
  };

  // Handle status toggle
  const handleToggleStatus = async (badgeTemplateId, currentStatus, templateName) => {
    try {
      const newStatus = !currentStatus;
      await updateBadgeTemplate(badgeTemplateId, { status: newStatus });
      toast.success(`Template "${templateName}" ${newStatus ? 'activated' : 'deactivated'} successfully`);
      fetchTemplates(currentPage, searchTerm);
    } catch (error) {
      console.error('Error updating template status:', error);
      toast.error('Failed to update template status');
    }
  };

  // Handle create template success
  const handleCreateSuccess = () => {
    setIsCreateModalOpen(false);
    fetchTemplates(currentPage, searchTerm);
  };

  // Add unsaved templates section before main templates
  const renderUnsavedTemplates = () => {
    if (unsavedTemplates.length === 0) return null;
    
    return (
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" sx={{ mb: 2, color: '#f57c00', display: 'flex', alignItems: 'center', gap: 1 }}>
          <WarningIcon />
          Draft Templates ({unsavedTemplates.length})
        </Typography>
        
        <Grid container spacing={2}>
          {unsavedTemplates.map((template) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={template.id}>
              <Card sx={{ 
                border: '2px solid #f57c00', 
                backgroundColor: 'rgba(245, 124, 0, 0.05)',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: '0 8px 25px rgba(245, 124, 0, 0.15)',
                }
              }}>
                <CardContent sx={{ pb: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    <Chip 
                      label="DRAFT" 
                      size="small" 
                      sx={{ 
                        backgroundColor: '#f57c00', 
                        color: 'white',
                        fontWeight: 'bold',
                        fontSize: '0.7rem'
                      }} 
                    />
                  </Box>
                  
                  <Typography variant="h6" sx={{ fontSize: '16px', fontWeight: 600, mb: 1 }}>
                    {template.name}
                  </Typography>
                  
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                    ID: {template.id}
                  </Typography>
                  
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                    Modified: {new Date(template.lastModified).toLocaleString()}
                  </Typography>
                </CardContent>
                
                <CardActions sx={{ pt: 0, justifyContent: 'space-between' }}>
                  <Button
                    size="small"
                    variant="contained"
                    onClick={() => navigate(`/id-designer/${template.id}`)}
                    sx={{ 
                      backgroundColor: '#f57c00',
                      '&:hover': { backgroundColor: '#e65100' }
                    }}
                  >
                    Continue Editing
                  </Button>
                  
                  <Button
                    size="small"
                    variant="outlined"
                    color="error"
                    onClick={() => handleDiscardDraft(template.id, template.name)}
                  >
                    Discard
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
        
        <Divider sx={{ mt: 3, mb: 2 }} />
      </Box>
    );
  };

  // Add discard handler
  const handleDiscardDraft = async (templateId, templateName) => {
    if (window.confirm(`Are you sure you want to discard all unsaved changes for "${templateName}"?`)) {
      try {
        removeTemplateFromStorage(templateId);
        setUnsavedTemplates(prev => prev.filter(t => t.id !== templateId));
        toast.success('Draft discarded successfully');
      } catch (error) {
        console.error('Error discarding draft:', error);
        toast.error('Failed to discard draft');
      }
    }
  };

  return (
    <div style={{ height: 'calc(100vh - 3.5rem)' }} className="flex flex-col px-8 py-4 pl-20 absolute mt-14 w-full">
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography 
          variant="h4" 
          sx={{ 
            fontWeight: 'normal', 
            fontSize: '24px', 
            mb: 3, 
            color: '#4F2683' 
          }}
        >
          Badge Template Management
        </Typography>
        
        {/* Search and Create Section */}
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          mb: 3,
          gap: 2
        }}>
          <TextField
            placeholder="Search templates..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            sx={{ 
              maxWidth: 400,
              '& .MuiOutlinedInput-root': {
                '&:hover fieldset': {
                  borderColor: '#4F2683',
                },
                '&.Mui-focused fieldset': {
                  borderColor: '#4F2683',
                },
              }
            }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon sx={{ color: '#6b7280' }} />
                </InputAdornment>
              ),
            }}
          />
          
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setIsCreateModalOpen(true)}
            sx={{
              backgroundColor: '#4F2683',
              '&:hover': {
                backgroundColor: '#3d1f66',
              },
              textTransform: 'none',
              fontWeight: 500,
              px: 3
            }}
          >
            Create Template
          </Button>
        </Box>

        {/* Results Summary */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            {loading ? 'Loading...' : `${totalTemplates} template${totalTemplates !== 1 ? 's' : ''} found`}
          </Typography>
          {searchTerm && (
            <Chip
              label={`Search: "${searchTerm}"`}
              size="small"
              onDelete={() => setSearchTerm('')}
              sx={{
                backgroundColor: 'rgba(79, 38, 131, 0.1)',
                color: '#4F2683',
                '& .MuiChip-deleteIcon': {
                  color: '#4F2683'
                }
              }}
            />
          )}
        </Box>
      </Box>

      {/* Unsaved Templates Section */}
      {renderUnsavedTemplates()}

      {/* Templates Grid */}
      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
            <Typography color="text.secondary">Loading templates...</Typography>
          </Box>
        ) : !Array.isArray(templates) || templates.length === 0 ? (
          <Box sx={{ 
            display: 'flex', 
            flexDirection: 'column',
            justifyContent: 'center', 
            alignItems: 'center', 
            height: 200,
            textAlign: 'center'
          }}>
            <Typography variant="h6" color="text.secondary" sx={{ mb: 1 }}>
              {searchTerm ? 'No templates found' : 'No templates created yet'}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              {searchTerm ? 'Try adjusting your search terms' : 'Create your first badge template to get started'}
            </Typography>
            {!searchTerm && (
              <Button
                variant="outlined"
                startIcon={<AddIcon />}
                onClick={() => setIsCreateModalOpen(true)}
                sx={{
                  borderColor: '#4F2683',
                  color: '#4F2683',
                  '&:hover': {
                    borderColor: '#4F2683',
                    backgroundColor: 'rgba(79, 38, 131, 0.04)',
                  },
                  textTransform: 'none'
                }}
              >
                Create Template
              </Button>
            )}
          </Box>
        ) : (
          <Grid container spacing={3}>
            {Array.isArray(templates) && templates.map((template) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={template.badge_template_id}>
                <Card
                  sx={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      boxShadow: '0 8px 25px rgba(79, 38, 131, 0.15)',
                    },
                    border: '1px solid rgba(79, 38, 131, 0.2)'
                  }}
                  onClick={() => handleOpenTemplate(template.badge_id)}
                >
                  <CardContent sx={{ flexGrow: 1, pb: 1 }}>
                    <Typography 
                      variant="h6" 
                      sx={{
                        fontSize: '16px',
                        fontWeight: 600,
                        color: '#4F2683',
                        mb: 1,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                      }}
                    >
                      {template.name}
                    </Typography>
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{ mb: 1 }}
                    >
                      ID: {template.badge_id}
                    </Typography>
                    <Chip
                      label={template.status !== false ? 'Active' : 'Inactive'}
                      size="small"
                      sx={{
                        backgroundColor: template.status !== false ? 'rgba(79, 38, 131, 0.1)' : 'rgba(107, 114, 128, 0.1)',
                        color: template.status !== false ? '#4F2683' : '#6b7280',
                        fontWeight: 500,
                        mb: 2
                      }}
                    />
                    <Typography 
                      variant="caption" 
                      color="text.secondary"
                      sx={{ display: 'block' }}
                    >
                      Created: {new Date(template.created_at).toLocaleDateString()}
                    </Typography>
                    {template.updated_at && template.updated_at !== template.created_at && (
                      <Typography 
                        variant="caption" 
                        color="text.secondary"
                        sx={{ display: 'block' }}
                      >
                        Modified: {new Date(template.updated_at).toLocaleDateString()}
                      </Typography>
                    )}
                  </CardContent>
                  <CardActions sx={{ pt: 0, justifyContent: 'space-between', flexWrap: 'wrap', gap: 1 }}>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Button
                        size="small"
                        startIcon={<EditIcon />}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleOpenTemplate(template.badge_template_id);
                        }}
                        sx={{
                          color: '#4F2683',
                          textTransform: 'none',
                          '&:hover': {
                            backgroundColor: 'rgba(79, 38, 131, 0.04)',
                          }
                        }}
                      >
                        Edit
                      </Button>
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleToggleStatus(template.badge_id, template.status, template.name);
                        }}
                        sx={{
                          color: template.status !== false ? '#4F2683' : '#6b7280',
                          '&:hover': {
                            backgroundColor: 'rgba(79, 38, 131, 0.04)',
                          }
                        }}
                        title={`${template.status !== false ? 'Deactivate' : 'Activate'} template`}
                      >
                        {template.status !== false ? <ToggleOnIcon /> : <ToggleOffIcon />}
                      </IconButton>
                    </Box>
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteTemplate(template.badge_id, template.name);
                      }}
                      sx={{
                        color: '#6b7280',
                        '&:hover': {
                          color: '#4F2683',
                          backgroundColor: 'rgba(79, 38, 131, 0.04)',
                        }
                      }}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </Box>

      {/* Pagination */}
      {totalPages > 1 && (
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'center', 
          mt: 4, 
          pb: 2 
        }}>
          <Pagination
            count={totalPages}
            page={currentPage}
            onChange={handlePageChange}
            color="primary"
            sx={{
              '& .MuiPaginationItem-root': {
                '&.Mui-selected': {
                  backgroundColor: '#4F2683',
                  '&:hover': {
                    backgroundColor: '#3d1f66',
                  }
                }
              }
            }}
          />
        </Box>
      )}

      {/* Create Template Modal */}
      <CreateTemplateModal
        open={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSuccess={handleCreateSuccess}
      />
    </div>
  );
};

export default TemplateManagement;





