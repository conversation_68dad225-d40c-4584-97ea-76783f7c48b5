import React, { useState, useRef, useEffect } from 'react';
import { 
  TextField, 
  Popper, 
  Paper, 
  List, 
  ListItem, 
  ListItemText, 
  Chip, 
  Box,
  Typography 
} from '@mui/material';

const TextInputWithVariables = ({ 
  value = '', 
  onChange, 
  availableVariables = [], 
  label = "Text",
  multiline = false,
  rows = 1,
  ...props 
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [cursorPosition, setCursorPosition] = useState(0);
  const inputRef = useRef(null);

  const handleInputChange = (e) => {
    const newValue = e.target.value;
    const cursorPos = e.target.selectionStart;
    
    onChange(newValue);
    setCursorPosition(cursorPos);
    
    // Check if user typed {{ to show suggestions
    const beforeCursor = newValue.substring(0, cursorPos);
    const openBraces = beforeCursor.lastIndexOf('{{');
    const closeBraces = beforeCursor.lastIndexOf('}}');
    
    if (openBraces > closeBraces && openBraces !== -1) {
      setAnchorEl(e.target);
      setShowSuggestions(true);
    } else {
      setShowSuggestions(false);
    }
  };

  const insertVariable = (variable) => {
    const beforeCursor = value.substring(0, cursorPosition);
    const afterCursor = value.substring(cursorPosition);
    const openBraces = beforeCursor.lastIndexOf('{{');
    
    let newValue;
    if (openBraces !== -1) {
      // Replace the {{ with the variable
      const varName = variable.name || variable.id || variable;
      newValue = beforeCursor.substring(0, openBraces) + `{{${varName}}}` + afterCursor;
    } else {
      // Insert at cursor position
      const varName = variable.name || variable.id || variable;
      newValue = beforeCursor + `{{${varName}}}` + afterCursor;
    }
    
    onChange(newValue);
    setShowSuggestions(false);
    
    // Focus back to input
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }, 100);
  };

  const renderTextWithChips = () => {
    if (!value) return null;
    
    const parts = value.split(/(\{\{[^}]+\}\})/);
    const hasVariables = parts.some(part => part.match(/\{\{[^}]+\}\}/));
    
    if (!hasVariables) return null;
    
    return (
      <Box sx={{ mt: 1 }}>
        <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 0.5 }}>
          Variables in text:
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
          {parts.map((part, index) => {
            if (part.match(/\{\{[^}]+\}\}/)) {
              const varName = part.replace(/[{}]/g, '');
              return (
                <Chip
                  key={index}
                  label={varName}
                  size="small"
                  color="primary"
                  variant="outlined"
                  sx={{ fontSize: '0.7rem', height: 20 }}
                />
              );
            }
            return null;
          })}
        </Box>
      </Box>
    );
  };

  return (
    <Box>
      <TextField
        {...props}
        inputRef={inputRef}
        label={label}
        value={value}
        onChange={handleInputChange}
        multiline={multiline}
        rows={rows}
        helperText="Type {{ to insert variables"
        size="small"
      />
      
      {renderTextWithChips()}
      
      <Popper
        open={showSuggestions && availableVariables.length > 0}
        anchorEl={anchorEl}
        placement="bottom-start"
        style={{ zIndex: 1300 }}
      >
        <Paper elevation={3} sx={{ maxHeight: 200, overflow: 'auto', minWidth: 200 }}>
          <List dense>
            {availableVariables.map((variable, index) => (
              <ListItem
                key={index}
                button
                onClick={() => insertVariable(variable)}
                sx={{ py: 0.5 }}
              >
                <ListItemText
                  primary={variable.display_name || variable.name || variable.id || variable}
                  secondary={variable.type || 'text'}
                  primaryTypographyProps={{ fontSize: '0.875rem' }}
                  secondaryTypographyProps={{ fontSize: '0.75rem' }}
                />
              </ListItem>
            ))}
          </List>
        </Paper>
      </Popper>
    </Box>
  );
};

export default TextInputWithVariables;



