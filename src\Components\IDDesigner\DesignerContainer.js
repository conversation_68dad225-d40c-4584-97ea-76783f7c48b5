import { useEffect, useCallback, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { Box, Paper, Toolbar, IconButton, Tooltip, Divider, TextField, Drawer, useMediaQuery, useTheme, Typography, CircularProgress } from '@mui/material';
import { toast } from 'react-toastify';
import {
  Undo as UndoIcon,
  Redo as RedoIcon,
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  Visibility as PreviewIcon,
  Save as SaveIcon,
  Home as HomeIcon,
  Restore as RestoreIcon,

  AspectRatio as CanvasIcon,
  Menu as MenuIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';

import DesignCanvas from './DesignCanvas';
import ToolPalette from './ToolPalette';
import PropertyPanel from './PropertyPanel';
import PreviewPane from './PreviewPane';
import TemplateManager from './TemplateManager';
import { updateBadgeTemplate } from '../../api/badge';
import { saveTemplateToStorage, markTemplateAsSynced } from '../../utils/templateStorage';
import imageManager from '../../utils/imageManager';

import {
  undo,
  redo,
  setCanvasScale,
  togglePreviewMode,
  toggleCanvasSettings,
  toggleTemplateManager,
  clearSelection,
  updateElement,
  deleteElement,
  duplicateElement,
  selectElement,
  markTemplateAsSaved,
  discardChanges,
  setDiscarding,
  saveToLocalStorage,
  checkTemplateChanges,
  setSaving,
} from '../../redux/idDesignerSlice';

const DesignerContainer = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [zoomInput, setZoomInput] = useState('');

  const [leftSidebarOpen, setLeftSidebarOpen] = useState(false);
  const [rightSidebarOpen, setRightSidebarOpen] = useState(false);

  const {
    canvasConfig,
    elements,
    selectedElementIds,
    history,
    previewMode,
    showCanvasSettings,
    showPreviewPane,
    showTemplateManager,
    currentTemplate,
    templateChanged,
    discarding,
    saving,
  } = useSelector((state) => state.idDesigner);

  // Add auto-save to local storage on any change
  useEffect(() => {
    if (currentTemplate.id) {
      dispatch(saveToLocalStorage());
      dispatch(checkTemplateChanges());
    }
  }, [elements, canvasConfig, currentTemplate.id, dispatch]);

  // Keyboard shortcuts
  const handleKeyDown = useCallback((event) => {
    // Don't handle keyboard shortcuts when typing in input fields
    if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
      return;
    }

    if (event.ctrlKey || event.metaKey) {
      switch (event.key) {
        case 'z':
          event.preventDefault();
          if (event.shiftKey) {
            dispatch(redo());
          } else {
            dispatch(undo());
          }
          break;
        case 'y':
          event.preventDefault();
          dispatch(redo());
          break;
        case 's':
          event.preventDefault();
          // TODO: Implement save functionality
          break;
        case 'o':
          event.preventDefault();
          dispatch(toggleTemplateManager());
          break;
        case 'c':
          event.preventDefault();
          // Copy selected elements (store in localStorage for now)
          if (selectedElementIds.length > 0) {
            const selectedElements = elements.filter(el => selectedElementIds.includes(el.id));
            localStorage.setItem('copiedElements', JSON.stringify(selectedElements));
          }
          break;
        case 'v':
          event.preventDefault();
          // Paste elements
          const copiedElements = localStorage.getItem('copiedElements');
          if (copiedElements) {
            try {
              const elementsToPaste = JSON.parse(copiedElements);
              elementsToPaste.forEach((element, index) => {
                dispatch(duplicateElement({
                  elementId: element.id,
                  offset: { x: 20 + (index * 10), y: 20 + (index * 10) }
                }));
              });
            } catch (error) {
              console.warn('Error pasting elements:', error);
            }
          }
          break;
        case 'd':
          event.preventDefault();
          // Duplicate selected elements
          selectedElementIds.forEach((elementId, index) => {
            dispatch(duplicateElement({
              elementId,
              offset: { x: 20 + (index * 10), y: 20 + (index * 10) }
            }));
          });
          break;
        case 'a':
          event.preventDefault();
          // Select all elements
          const allElementIds = elements.map(el => el.id);
          dispatch(selectElement(allElementIds));
          break;
        default:
          break;
      }
    } else {
      // Handle non-modifier keys
      switch (event.key) {
        case 'Delete':
        case 'Backspace':
          event.preventDefault();
          // Delete selected elements
          selectedElementIds.forEach(elementId => {
            dispatch(deleteElement(elementId));
          });
          break;
        case 'Escape':
          event.preventDefault();
          // Clear selection
          dispatch(clearSelection());
          break;
        case 'ArrowUp':
          event.preventDefault();
          // Move selected elements up
          selectedElementIds.forEach(elementId => {
            const element = elements.find(el => el.id === elementId);
            if (element) {
              const moveDistance = event.shiftKey ? 10 : 1;
              dispatch(updateElement({
                elementId,
                properties: { y: Math.max(0, element.y - moveDistance) }
              }));
            }
          });
          break;
        case 'ArrowDown':
          event.preventDefault();
          // Move selected elements down
          selectedElementIds.forEach(elementId => {
            const element = elements.find(el => el.id === elementId);
            if (element) {
              const moveDistance = event.shiftKey ? 10 : 1;
              dispatch(updateElement({
                elementId,
                properties: { y: Math.min(canvasConfig.height - element.height, element.y + moveDistance) }
              }));
            }
          });
          break;
        case 'ArrowLeft':
          event.preventDefault();
          // Move selected elements left
          selectedElementIds.forEach(elementId => {
            const element = elements.find(el => el.id === elementId);
            if (element) {
              const moveDistance = event.shiftKey ? 10 : 1;
              dispatch(updateElement({
                elementId,
                properties: { x: Math.max(0, element.x - moveDistance) }
              }));
            }
          });
          break;
        case 'ArrowRight':
          event.preventDefault();
          // Move selected elements right
          selectedElementIds.forEach(elementId => {
            const element = elements.find(el => el.id === elementId);
            if (element) {
              const moveDistance = event.shiftKey ? 10 : 1;
              dispatch(updateElement({
                elementId,
                properties: { x: Math.min(canvasConfig.width - element.width, element.x + moveDistance) }
              }));
            }
          });
          break;
        default:
          break;
      }
    }
  }, [dispatch, selectedElementIds, elements, canvasConfig]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  // Sync zoom input with canvas scale
  useEffect(() => {
    setZoomInput(Math.round(canvasConfig.scale * 100).toString());
  }, [canvasConfig.scale]);

  const handleZoomIn = () => {
    const newScale = Math.min(canvasConfig.scale * 1.2, 3);
    dispatch(setCanvasScale(newScale));
  };

  const handleZoomOut = () => {
    const newScale = Math.max(canvasConfig.scale / 1.2, 0.1);
    dispatch(setCanvasScale(newScale));
  };

  const handleZoomInputChange = (e) => {
    const value = e.target.value;
    setZoomInput(value);
  };

  const handleZoomInputSubmit = (e) => {
    if (e.key === 'Enter' || e.type === 'blur') {
      const numValue = parseFloat(zoomInput);
      if (!isNaN(numValue) && numValue > 0) {
        const scale = Math.max(0.1, Math.min(3, numValue / 100));
        dispatch(setCanvasScale(scale));
      }
      setZoomInput(Math.round(canvasConfig.scale * 100).toString());
    }
  };

  const handleCanvasSettingsClick = () => {
    dispatch(clearSelection());
    dispatch(toggleCanvasSettings());
  };

  const handleDiscardChanges = async () => {
    if (!templateChanged || !currentTemplate.id) return;
    
    try {
      dispatch(setDiscarding(true));
      
      // Remove from local storage
      localStorage.removeItem(`template_${currentTemplate.id}`);
      
      // Dispatch discard action (this will restore API state and add to history)
      dispatch(discardChanges());
      
      toast.success('Changes discarded successfully');
    } catch (error) {
      console.error('Error discarding changes:', error);
      toast.error('Failed to discard changes');
    } finally {
      dispatch(setDiscarding(false));
    }
  };

  const handleSave = async () => {
    if (!currentTemplate.id) {
      toast.error('No template loaded to save');
      return;
    }

    try {
      dispatch(setSaving(true));
      
      // Extract variables from elements
      const variables = [];
      const elementsToSave = [...elements];

      elements.forEach(element => {
        if (element.type === 'text' && element.text) {
          const matches = element.text.match(/\{\{([^}]+)\}\}/g);
          if (matches) {
            matches.forEach(match => {
              const varName = match.replace(/[{}]/g, '');
              if (!variables.find(v => v.name === varName)) {
                variables.push({
                  name: varName,
                  type: 'text',
                  element_id: element.id
                });
              }
            });
          }
        } else if ((element.type === 'qrcode' || element.type === 'barcode') && element.data) {
          const matches = element.data.match(/\{\{([^}]+)\}\}/g);
          if (matches) {
            matches.forEach(match => {
              const varName = match.replace(/[{}]/g, '');
              if (!variables.find(v => v.name === varName)) {
                variables.push({
                  name: varName,
                  type: element.type,
                  element_id: element.id
                });
              }
            });
          }
        } else if (element.type === 'image' && element.hasFile && imageManager.hasPendingUpload(element.id)) {
          element.hasFile = true;
        }
      });

      // Get images for upload
      const imagesToUpload = imageManager.getImagesForUpload();

      const templateData = {
        name: currentTemplate.name || 'Untitled Template',
        content: {
          canvasConfig: canvasConfig,
          elements: elementsToSave
        },
        variables: variables,
        schema: currentTemplate.schema,
        key: currentTemplate.key,
        format: currentTemplate.format
      };

      // Save to API with images
      const response = await updateBadgeTemplate(currentTemplate.id, templateData, imagesToUpload);

      // Update local storage to mark as synced
      const localData = {
        id: currentTemplate.id,
        name: templateData.name,
        content: templateData.content,
        schema: templateData.schema,
        key: templateData.key,
        format: templateData.format,
        lastModified: new Date().toISOString(),
        locallyModified: false // Mark as synced
      };
      localStorage.setItem(`template_${currentTemplate.id}`, JSON.stringify(localData));

      // Mark images as uploaded if any were included
      if (imagesToUpload.length > 0 && response.data?.images) {
        imageManager.markImagesAsUploaded(response.data.images);
        toast.success(`Template saved successfully with ${imagesToUpload.length} image(s)`);
      } else {
        toast.success('Template saved successfully');
      }

      // Mark as saved
      dispatch(markTemplateAsSaved());
    } catch (error) {
      console.error('Error saving template:', error);
      toast.error('Failed to save template');
    } finally {
      dispatch(setSaving(false));
    }
  };

  return (
    <Box sx={{
      height: 'calc(100%)',
      display: 'flex',
      flexDirection: 'column',
      bgcolor: '#f5f5f5',
      borderRadius: 2,
      overflow: 'hidden',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
      border: '1px solid #e5e7eb'
    }}>
      {/* Top Toolbar */}
      <Paper elevation={1} sx={{ zIndex: 1000 }}>
        <Toolbar variant="dense" sx={{ minHeight: 48, gap: 1 }}>
          {/* Mobile Menu Buttons */}
          {isMobile && (
            <>
              <Tooltip title="Tools">
                <IconButton
                  size="small"
                  onClick={() => setLeftSidebarOpen(true)}
                  sx={{
                    color: '#4F2683',
                    '&:hover': {
                      backgroundColor: 'rgba(79, 38, 131, 0.04)',
                    }
                  }}
                >
                  <MenuIcon />
                </IconButton>
              </Tooltip>

              <Tooltip title="Properties">
                <IconButton
                  size="small"
                  onClick={() => setRightSidebarOpen(true)}
                  sx={{
                    color: '#4F2683',
                    '&:hover': {
                      backgroundColor: 'rgba(79, 38, 131, 0.04)',
                    }
                  }}
                >
                  <SettingsIcon />
                </IconButton>
              </Tooltip>

              <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />
            </>
          )}

          {/* File Operations */}
          <Tooltip title="Back to Template Management">
            <IconButton
              size="small"
              onClick={() => navigate('/template-management')}
              sx={{
                color: '#4F2683',
                '&:hover': {
                  backgroundColor: 'rgba(79, 38, 131, 0.04)',
                }
              }}
            >
              <HomeIcon />
            </IconButton>
          </Tooltip>

          {/* Save Button with Loader */}
          <Tooltip title={templateChanged ? "Save Changes" : "No Changes to Save"}>
            <IconButton
              size="small"
              onClick={handleSave}
              disabled={saving || !templateChanged}
              sx={{
                color: templateChanged ? '#f57c00' : '#4caf50'
              }}
            >
              {saving ? <CircularProgress size={16} /> : <SaveIcon />}
            </IconButton>
          </Tooltip>

          <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

          {/* History Operations */}
          <Tooltip title="Undo (Ctrl+Z)">
            <IconButton
              size="small"
              onClick={() => dispatch(undo())}
              disabled={history.past.length === 0}
            >
              <UndoIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Redo (Ctrl+Y)">
            <IconButton
              size="small"
              onClick={() => dispatch(redo())}
              disabled={history.future.length === 0}
            >
              <RedoIcon />
            </IconButton>
          </Tooltip>

          <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

          {/* Zoom Controls */}
          <Tooltip title="Zoom Out">
            <IconButton size="small" onClick={handleZoomOut}>
              <ZoomOutIcon />
            </IconButton>
          </Tooltip>

          <TextField
            value={zoomInput}
            onChange={handleZoomInputChange}
            onKeyDown={handleZoomInputSubmit}
            onBlur={handleZoomInputSubmit}
            size="small"
            variant="outlined"
            sx={{
              width: 70,
              '& .MuiOutlinedInput-root': {
                height: 32,
                margin: 0,
                fontSize: '0.875rem',
                '& input': {
                  textAlign: 'center',
                  padding: '4px 8px',
                }
              }
            }}
            slotProps={{
              input: {
                endAdornment: '%'
              }
            }}
          />

          <Tooltip title="Zoom In">
            <IconButton size="small" onClick={handleZoomIn}>
              <ZoomInIcon />
            </IconButton>
          </Tooltip>

          <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

          {/* View Controls */}
          <Tooltip title="Toggle Preview Mode">
            <IconButton
              size="small"
              onClick={() => dispatch(togglePreviewMode())}
              color={previewMode ? 'primary' : 'default'}
            >
              <PreviewIcon />
            </IconButton>
          </Tooltip>

          {/* Discard Changes Button */}
          <Tooltip title="Discard Changes">
            <IconButton
              size="small"
              onClick={handleDiscardChanges}
              disabled={!templateChanged || discarding}
              sx={{ 
                color: templateChanged ? '#f44336' : '#ccc',
                '&:disabled': { color: '#ccc' }
              }}
            >
              {discarding ? <CircularProgress size={16} /> : <RestoreIcon />}
            </IconButton>
          </Tooltip>

          {/* Canvas Settings */}
          <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Tooltip title="Canvas Settings">
              <IconButton
                size="small"
                color={showCanvasSettings ? 'primary' : 'default'}
                onClick={handleCanvasSettingsClick}
              >
                <CanvasIcon />
              </IconButton>
            </Tooltip>
            <Box sx={{ fontSize: '0.75rem', color: 'text.secondary' }}>
              {canvasConfig.width} × {canvasConfig.height}
            </Box>
          </Box>
        </Toolbar>
      </Paper>

      {/* Main Content Area */}
      <Box sx={{
        width: '100%',
        height: 'calc(100vh - 48px)', // Subtract toolbar height
        display: 'flex',
        overflow: 'hidden',
        position: 'relative'
      }}>
        {/* Desktop Layout */}
        {!isMobile && (
          <>
            {/* Left Sidebar - Tool Palette */}
            <Paper
              elevation={2}
              sx={{
                width: '20%',
                display: 'flex',
                flexDirection: 'column',
                borderRadius: 0,
                borderRight: 1,
                borderColor: 'divider',
                bgcolor: 'white',
                boxShadow: '2px 0 4px rgba(0, 0, 0, 0.1)',
              }}
            >
              <ToolPalette />
            </Paper>

            {/* Center - Canvas Area */}
            <Box
              sx={{
                width: '60%',
                display: 'flex',
                flexDirection: 'column',
                position: 'relative',
                overflow: 'hidden',
              }}
            >
              <DesignCanvas />
            </Box>

            {/* Right Sidebar - Property Panel */}
            <Paper
              elevation={2}
              sx={{
                width: '20%',
                display: 'flex',
                flexDirection: 'column',
                borderRadius: 0,
                borderLeft: 1,
                borderColor: 'divider',
                bgcolor: 'white',
                boxShadow: '-2px 0 4px rgba(0, 0, 0, 0.1)',
              }}
            >
              <PropertyPanel />
            </Paper>
          </>
        )}

        {/* Mobile Layout */}
        {isMobile && (
          <>
            {/* Full-width Canvas Area */}
            <Box
              sx={{
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                position: 'relative',
                overflow: 'hidden',
              }}
            >
              <DesignCanvas />
            </Box>

            {/* Mobile Left Drawer - Tool Palette */}
            <Drawer
              anchor="left"
              open={leftSidebarOpen}
              onClose={() => setLeftSidebarOpen(false)}
              sx={{
                '& .MuiDrawer-paper': {
                  width: 280,
                  boxSizing: 'border-box',
                },
              }}
            >
              <ToolPalette />
            </Drawer>

            {/* Mobile Right Drawer - Property Panel */}
            <Drawer
              anchor="right"
              open={rightSidebarOpen}
              onClose={() => setRightSidebarOpen(false)}
              sx={{
                '& .MuiDrawer-paper': {
                  width: 320,
                  boxSizing: 'border-box',
                },
              }}
            >
              <PropertyPanel />
            </Drawer>
          </>
        )}

        {/* Preview Pane */}
        {showPreviewPane && (
          <Paper
            elevation={1}
            sx={{
              position: 'absolute',
              right: '20%',
              top: 0,
              width: 400,
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              borderRadius: 0,
              borderLeft: 1,
              borderColor: 'divider',
              zIndex: 1000,
            }}
          >
            <PreviewPane />
          </Paper>
        )}
      </Box>

      {/* Template Manager Modal */}
      {showTemplateManager && <TemplateManager />}
    </Box>
  );
};

export default DesignerContainer;






