import React from 'react';
import { useDispatch } from 'react-redux';
import {
  Box,
  Typography,

  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  Paper,
  Tooltip,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  TextFields as TextIcon,
  Image as ImageIcon,
  QrCode as QRCodeIcon,
  CropFree as BarcodeIcon,
  Crop75 as RectangleIcon,
  Circle as CircleIcon,
  Star as StarIcon,
  Person as PersonIcon,
  Timeline as LineIcon,
  ChangeHistory as TriangleIcon,
  Hexagon as HexagonIcon,
} from '@mui/icons-material';

import { addElement } from '../../redux/idDesignerSlice';

const ToolPalette = () => {
  const dispatch = useDispatch();

  const handleAddElement = (elementType, properties = {}) => {
    const defaultProperties = {
      text: {
        type: 'text',
        text: 'Sample Text',
        fontSize: 16,
        fontFamily: 'Arial',
        color: '#4f2683',
        textAlign: 'center',
        width: 120,
        height: 30,
      },
      staticImage: {
        type: 'image',
        src: null,
        width: 100,
        height: 100,
        placeholder: 'Static Image',
        imageType: 'static',
        acceptedTypes: 'image/jpeg,image/png,image/jpg,image/svg+xml',
        isDynamic: false,
      },
      qrcode: {
        type: 'qrcode',
        data: '{{id}}',
        size: 80,
        width: 80,
        height: 80,
        errorCorrectionLevel: 'M',
      },
      barcode: {
        type: 'barcode',
        data: '{{id}}',
        format: 'CODE128',
        width: 120,
        height: 40,
      },
      rectangle: {
        type: 'shape',
        shapeType: 'rectangle',
        width: 100,
        height: 60,
        fill: 'rgba(79, 38, 131, 0.1)',
        stroke: '#4f2683',
        strokeWidth: 1,
      },
    };

    const elementProps = { ...defaultProperties[elementType], ...properties };
    dispatch(addElement(elementProps));
  };

  const ToolButton = ({ icon, label, onClick, color = 'primary', tooltip, iconColor }) => (
    <Tooltip title={tooltip || label}>
      <Paper
        elevation={1}
        sx={{
          p: 1.5,
          cursor: 'pointer',
          textAlign: 'center',
          transition: 'all 0.2s',
          '&:hover': {
            elevation: 3,
            bgcolor: 'action.hover',
          },
        }}
        onClick={onClick}
      >
        <Box sx={{ color: iconColor || `${color}.main`, mb: 0.5 }}>
          {icon}
        </Box>
        <Typography variant="caption" sx={{ fontSize: '0.7rem' }}>
          {label}
        </Typography>
      </Paper>
    </Tooltip>
  );

  return (
    <Box sx={{ height: '100%', overflow: 'auto' }}>
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h6" sx={{ fontSize: '1rem', fontWeight: 600 }}>
          Design Tools
        </Typography>
      </Box>

      {/* Text Elements - Single type only */}
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">Text Elements</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={1}>
            <Grid item xs={12}>
              <ToolButton
                icon={<TextIcon />}
                label="Text"
                onClick={() => handleAddElement('text')}
                tooltip="Add text with variable support"
              />
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>

      {/* Image Elements - Remove photo, keep static image only */}
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">Image Elements</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={1}>
            <Grid item xs={12}>
              <ToolButton
                icon={<ImageIcon />}
                label="Image"
                onClick={() => handleAddElement('staticImage')}
                tooltip="Upload your own image"
              />
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>

      {/* Code Elements */}
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">Code Elements</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={1}>
            <Grid item xs={6}>
              <ToolButton
                icon={<QrCodeIcon />}
                label="QR Code"
                onClick={() => handleAddElement('qrcode')}
                color="primary"
              />
            </Grid>
            <Grid item xs={6}>
              <ToolButton
                icon={<BarcodeIcon />}
                label="Barcode"
                onClick={() => handleAddElement('barcode')}
                color="primary"
              />
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>

      {/* Shape Elements */}
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">Shape Elements</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={1}>
            <Grid item xs={6}>
              <ToolButton
                icon={<RectangleIcon />}
                label="Rectangle"
                onClick={() => handleAddElement('rectangle')}
                color="secondary"
              />
            </Grid>
            <Grid item xs={6}>
              <ToolButton
                icon={<CircleIcon />}
                label="Circle"
                onClick={() => handleAddElement('circle')}
                color="secondary"
              />
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>


    </Box>
  );
};

export default ToolPalette;








