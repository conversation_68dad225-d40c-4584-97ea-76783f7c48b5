import { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { toast } from 'react-toastify';
import DesignerContainer from '../../Components/IDDesigner/DesignerContainer';
import { 
  loadTemplate, 
  markTemplateAsSaved, 
  setAvailableVariables, 
  setLoadingVariables,
  checkTemplateChanges 
} from '../../redux/idDesignerSlice';
import { getBadgeTemplateById } from '../../api/badge';
import {
  loadTemplateFromStorage,
  saveTemplateToStorage,
  isTemplateLocallyModified,
  markTemplateAsSynced
} from '../../utils/templateStorage';
import imageManager from '../../utils/imageManager';
import { migrateElementImages, needsImageMigration } from '../../utils/imageMigration';

const IDDesigner = () => {
  const { templateId } = useParams();
  const dispatch = useDispatch();

  useEffect(() => {
    const loadTemplateData = async () => {
      if (templateId) {
        try {
          // First check if we have local data
          const localStorageKey = `template_${templateId}`;
          const localDataString = localStorage.getItem(localStorageKey);
          let localData = null;
          
          if (localDataString) {
            try {
              localData = JSON.parse(localDataString);
            } catch (e) {
              console.warn('Error parsing local template data:', e);
            }
          }

          // Fetch from API
          const response = await getBadgeTemplateById(templateId);
          const apiData = response.data;

          let templateToLoad = null;
          let useLocalData = false;

          // Check if local data exists and is more recent
          if (localData && localData.locallyModified) {
            const localModified = new Date(localData.lastModified);
            const apiModified = new Date(apiData.updated_at || apiData.created_at);
            
            if (localModified > apiModified) {
              useLocalData = true;
            }
          }

          if (useLocalData && localData) {
            // Use local data
            let elements = localData.content?.elements || localData.elements || [];

            // Migrate base64 images if needed
            if (needsImageMigration(elements)) {
              elements = migrateElementImages(elements);
              toast.info('Migrating images to new format...');
            }

            templateToLoad = {
              id: templateId,
              name: localData.name,
              lastModified: localData.lastModified,
              canvasConfig: localData.content?.canvasConfig || localData.canvasConfig || {},
              elements: elements,
              schema: localData.schema || apiData.schema,
              key: localData.key || apiData.key,
              format: localData.format || apiData.format
            };
            
            toast.info(`Loaded local version of "${localData.name}" (has unsaved changes)`);
          } else {
            // Use API data
            let elements = apiData.content?.elements || apiData.config?.elements || [];

            // Migrate base64 images if needed
            if (needsImageMigration(elements)) {
              elements = migrateElementImages(elements);
              toast.info('Migrating images to new format...');
            }

            templateToLoad = {
              id: apiData.badge_id,
              name: apiData.name,
              lastModified: apiData.updated_at || apiData.created_at,
              canvasConfig: apiData.content?.canvasConfig || apiData.config?.canvasConfig || {},
              elements: elements,
              schema: apiData.schema,
              key: apiData.key,
              format: apiData.format
            };

            // Save API data to local storage
            const templateData = {
              id: templateToLoad.id,
              name: templateToLoad.name,
              content: {
                canvasConfig: templateToLoad.canvasConfig,
                elements: templateToLoad.elements
              },
              schema: templateToLoad.schema,
              key: templateToLoad.key,
              format: templateToLoad.format,
              lastModified: templateToLoad.lastModified,
              locallyModified: false
            };
            
            localStorage.setItem(localStorageKey, JSON.stringify(templateData));
            toast.success(`Template "${templateToLoad.name}" loaded successfully`);
          }

          // Load the template into the designer
          dispatch(loadTemplate(templateToLoad));

          // Load available variables if schema exists
          if (templateToLoad.schema) {
            dispatch(setLoadingVariables(true));
            try {
              const variablesResponse = await getSchemaVariables(templateToLoad.schema);
              dispatch(setAvailableVariables(variablesResponse.data || []));
            } catch (error) {
              console.error('Error loading variables:', error);
              dispatch(setAvailableVariables([]));
            } finally {
              dispatch(setLoadingVariables(false));
            }
          }

          // Check if template has changes
          dispatch(checkTemplateChanges());

        } catch (error) {
          console.error('Error loading template:', error);

          // Try to load from local storage as fallback
          const localStorageKey = `template_${templateId}`;
          const localDataString = localStorage.getItem(localStorageKey);
          
          if (localDataString) {
            try {
              const localData = JSON.parse(localDataString);
              let elements = localData.content?.elements || localData.elements || [];

              // Migrate base64 images if needed
              if (needsImageMigration(elements)) {
                elements = migrateElementImages(elements);
                toast.info('Migrating images to new format...');
              }

              dispatch(loadTemplate({
                id: templateId,
                name: localData.name,
                lastModified: localData.lastModified,
                canvasConfig: localData.content?.canvasConfig || localData.canvasConfig || {},
                elements: elements,
                schema: localData.schema,
                key: localData.key,
                format: localData.format
              }));
              
              toast.warning(`Loaded local version of "${localData.name}" (API unavailable)`);
            } catch (parseError) {
              console.error('Error parsing local data:', parseError);
              toast.error('Failed to load template');
            }
          } else {
            toast.error('Failed to load template');
          }
        }
      }
    };

    loadTemplateData();
  }, [templateId, dispatch]);

  // Cleanup image manager when component unmounts
  useEffect(() => {
    return () => {
      imageManager.clear();
    };
  }, []);

  return (
    <div style={{ height: 'calc(100vh - 3.5rem)' }} className="flex flex-col px-8 py-4 pl-20 absolute mt-14 w-full">
      <h2 className="font-normal text-[24px] mb-4 text-[#4F2683]">
        ID Card Designer{templateId ? ` - ${templateId}` : ''}
      </h2>
      <div className="flex-1 overflow-hidden">
        <DesignerContainer />
      </div>
    </div>
  );
};

export default IDDesigner;


